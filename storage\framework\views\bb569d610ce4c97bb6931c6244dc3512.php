<?php $__env->startSection('title', 'اطلب سيارتك - الخطوة الأولى: اختيار الماركة'); ?>

<?php $__env->startSection('meta_description', 'اطلب سيارتك المثالية من موتور لاين - الخطوة الأولى: اختيار الماركة المفضلة لديك'); ?>

<?php $__env->startSection('content'); ?>

<section class="steps-progress py-4 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="steps-container">
                    <div class="step active">
                        <div class="step-number">1</div>
                        <div class="step-title">اختيار الماركة</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-title">اختيار الموديل</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-title">اختيار السنة</div>
                    </div>
                    <div class="step-line"></div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-title">التفاصيل النهائية</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<section class="request-car-section py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                
                <div class="text-center mb-5">
                    <h1 class="page-title">
                        <i class="fas fa-car me-3 text-primary"></i>
                        اطلب سيارتك
                    </h1>
                    <p class="page-subtitle text-muted">
                        الخطوة الأولى: اختر الماركة المفضلة لديك
                    </p>
                </div>

                
                <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                
                <div class="brands-grid">
                    <div class="row g-4">
                        <?php $__empty_1 = true; $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <div class="col-lg-3 col-md-4 col-sm-6">
                                <div class="brand-card h-100" data-brand-id="<?php echo e($brand->id); ?>">
                                    <div class="brand-card-inner">
                                        
                                        <div class="brand-logo">
                                            <?php if($brand->hasMedia('brand_logos')): ?>
                                                <img src="<?php echo e($brand->getFirstMediaUrl('brand_logos', 'thumb')); ?>" 
                                                     alt="<?php echo e($brand->name); ?>" 
                                                     class="img-fluid">
                                            <?php else: ?>
                                                <div class="brand-placeholder">
                                                    <i class="fas fa-car fa-2x text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        
                                        <div class="brand-name">
                                            <h5 class="mb-0"><?php echo e($brand->name); ?></h5>
                                        </div>

                                        
                                        <div class="selection-indicator">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="fas fa-car fa-3x text-muted mb-3"></i>
                                    <h4 class="text-muted">لا توجد ماركات متاحة حالياً</h4>
                                    <p class="text-muted">يرجى المحاولة مرة أخرى لاحقاً</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                
                <div class="navigation-buttons mt-5 text-center">
                    <a href="<?php echo e(route('site.cars.index')); ?>" class="btn btn-outline-secondary btn-lg me-3">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للسيارات
                    </a>
                    <button type="button" id="nextStepBtn" class="btn btn-primary btn-lg" disabled>
                        التالي: اختيار الموديل
                        <i class="fas fa-arrow-left ms-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Steps Progress */
.steps-container {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background: var(--bs-primary);
    color: white;
}

.step-title {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.step.active .step-title {
    color: var(--bs-primary);
    font-weight: 600;
}

.step-line {
    flex: 1;
    height: 2px;
    background: #e9ecef;
    margin: 0 1rem;
    position: relative;
    top: -16px;
}

/* Brands Grid */
.brands-grid {
    margin-top: 2rem;
}

.brand-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    background: white;
    border: 2px solid #e9ecef;
    position: relative;
}

.brand-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border-color: var(--bs-primary);
}

.brand-card.selected {
    border-color: var(--bs-primary);
    background: linear-gradient(135deg, var(--bs-primary) 0%, #0056b3 100%);
    color: white;
}

.brand-card-inner {
    padding: 2rem 1rem;
    text-align: center;
    position: relative;
}

.brand-logo {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.brand-logo img {
    max-height: 60px;
    max-width: 100%;
    object-fit: contain;
}

.brand-placeholder {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.brand-name h5 {
    font-weight: 600;
    transition: color 0.3s ease;
}

.brand-card.selected .brand-name h5 {
    color: white;
}

.selection-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    color: var(--bs-success);
    font-size: 1.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.brand-card.selected .selection-indicator {
    opacity: 1;
    color: white;
}

/* Navigation Buttons */
.navigation-buttons {
    border-top: 1px solid #e9ecef;
    padding-top: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .steps-container {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .step-line {
        display: none;
    }
    
    .brand-card-inner {
        padding: 1.5rem 1rem;
    }
    
    .brand-logo {
        height: 60px;
    }
    
    .brand-logo img {
        max-height: 40px;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const brandCards = document.querySelectorAll('.brand-card');
    const nextStepBtn = document.getElementById('nextStepBtn');
    let selectedBrandId = null;

    // Handle brand selection
    brandCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selection from all cards
            brandCards.forEach(c => c.classList.remove('selected'));
            
            // Add selection to clicked card
            this.classList.add('selected');
            
            // Get selected brand ID
            selectedBrandId = this.dataset.brandId;
            
            // Enable next button
            nextStepBtn.disabled = false;
            
            // Update next button URL
            nextStepBtn.onclick = function() {
                window.location.href = `<?php echo e(route('site.request-car.step2', '')); ?>/${selectedBrandId}`;
            };
        });
    });

    // Handle keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && selectedBrandId) {
            window.location.href = `<?php echo e(route('site.request-car.step2', '')); ?>/${selectedBrandId}`;
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\Modules/OrderManagement\Resources/views/site/request_car/step1.blade.php ENDPATH**/ ?>
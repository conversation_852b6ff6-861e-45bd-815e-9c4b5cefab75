<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

/**
 * Migration لإضافة حقول طلبات السيارات المخصصة إلى جدول orders
 *
 * يضيف هذا Migration الحقول المطلوبة لدعم عملية "اطلب سيارتك"
 * حيث يمكن للعملاء طلب سيارات بمواصفات مخصصة غير متوفرة في المخزون
 */
class AddCustomCarRequestFieldsToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            // بيانات العميل لطلبات السيارات المخصصة (للزوار غير المسجلين)
            $table->string('customer_name', 100)->nullable()->after('user_id')
                  ->comment('اسم العميل لطلبات السيارات المخصصة');

            $table->string('customer_phone', 20)->nullable()->after('customer_name')
                  ->comment('رقم جوال العميل لطلبات السيارات المخصصة');

            $table->string('customer_email', 150)->nullable()->after('customer_phone')
                  ->comment('بريد العميل الإلكتروني لطلبات السيارات المخصصة');

            // بيانات السيارة المطلوبة (للطلبات المخصصة)
            $table->unsignedBigInteger('requested_brand_id')->nullable()->after('car_id')
                  ->comment('معرف الماركة المطلوبة للسيارة المخصصة');

            $table->unsignedBigInteger('requested_model_id')->nullable()->after('requested_brand_id')
                  ->comment('معرف الموديل المطلوب للسيارة المخصصة');

            $table->unsignedBigInteger('requested_year_id')->nullable()->after('requested_model_id')
                  ->comment('معرف سنة الصنع المطلوبة للسيارة المخصصة');

            // تفاصيل السيارة المطلوبة
            $table->string('preferred_color', 50)->nullable()->after('requested_year_id')
                  ->comment('اللون المفضل للسيارة المخصصة');

            $table->text('additional_features')->nullable()->after('preferred_color')
                  ->comment('الميزات الإضافية المطلوبة للسيارة المخصصة');

            $table->string('budget_range', 50)->nullable()->after('additional_features')
                  ->comment('نطاق الميزانية المتوقعة للسيارة المخصصة');

            $table->string('delivery_preference', 100)->nullable()->after('budget_range')
                  ->comment('تفضيل التسليم للسيارة المخصصة');

            $table->text('notes')->nullable()->after('delivery_preference')
                  ->comment('ملاحظات إضافية من العميل');

            // تاريخ الطلب
            $table->timestamp('order_date')->nullable()->after('notes')
                  ->comment('تاريخ تقديم الطلب');

            // إضافة المفاتيح الخارجية
            $table->foreign('requested_brand_id')
                  ->references('id')
                  ->on('brands')
                  ->onDelete('set null')
                  ->onUpdate('cascade');

            $table->foreign('requested_model_id')
                  ->references('id')
                  ->on('car_models')
                  ->onDelete('set null')
                  ->onUpdate('cascade');

            $table->foreign('requested_year_id')
                  ->references('id')
                  ->on('manufacturing_years')
                  ->onDelete('set null')
                  ->onUpdate('cascade');

            // إضافة فهارس للبحث السريع
            $table->index('customer_phone');
            $table->index('customer_email');
            $table->index('requested_brand_id');
            $table->index('requested_model_id');
            $table->index('requested_year_id');
            $table->index('order_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            // حذف المفاتيح الخارجية أولاً
            $table->dropForeign(['requested_brand_id']);
            $table->dropForeign(['requested_model_id']);
            $table->dropForeign(['requested_year_id']);

            // حذف الفهارس
            $table->dropIndex(['customer_phone']);
            $table->dropIndex(['customer_email']);
            $table->dropIndex(['requested_brand_id']);
            $table->dropIndex(['requested_model_id']);
            $table->dropIndex(['requested_year_id']);
            $table->dropIndex(['order_date']);

            // حذف الأعمدة
            $table->dropColumn([
                'customer_name',
                'customer_phone',
                'customer_email',
                'requested_brand_id',
                'requested_model_id',
                'requested_year_id',
                'preferred_color',
                'additional_features',
                'budget_range',
                'delivery_preference',
                'notes',
                'order_date',
            ]);
        });
    }
}



<?php
    $modalId = $modalId ?? 'serviceRequestModal';
    $formId = $formId ?? 'serviceRequestForm';
    $serviceName = isset($service) ? $service->name : '';
    $serviceId = isset($service) ? $service->id : '';
    $servicePrice = isset($service) ? $service->price : '';
    $serviceCurrency = isset($service) ? $service->currency : 'ريال';
?>


<div class="modal fade" id="<?php echo e($modalId); ?>" tabindex="-1" aria-labelledby="<?php echo e($modalId); ?>Label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header brand-bg-primary text-white">
                <h5 class="modal-title" id="<?php echo e($modalId); ?>Label">
                    <i class="fas fa-paper-plane me-2"></i>
                    طلب خدمة
                    <?php if($serviceName): ?>
                        : <span id="modal-service-name"><?php echo e($serviceName); ?></span>
                    <?php endif; ?>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                
                <?php if($servicePrice): ?>
                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-tag me-2"></i>
                        <div>
                            <strong>سعر الخدمة:</strong>
                            <span class="fs-5 fw-bold text-primary"><?php echo e(number_format($servicePrice, 2)); ?> <?php echo e($serviceCurrency); ?></span>
                            <small class="text-muted d-block">شامل ضريبة القيمة المضافة</small>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <form id="<?php echo e($formId); ?>" action="<?php echo e(route('site.services.request')); ?>" method="POST">
                    <?php echo csrf_field(); ?>

                    
                    <input type="hidden" id="service_id" name="service_id" value="<?php echo e($serviceId); ?>">

                    
                    <div class="mb-3">
                        <label for="service_name_display" class="form-label">الخدمة المطلوبة</label>
                        <input type="text" class="form-control" id="service_name_display"
                               value="<?php echo e($serviceName); ?>" readonly>
                    </div>

                    
                    <div class="mb-3">
                        <label for="full_name" class="form-label">
                            الاسم الكامل <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="full_name" name="full_name"
                               placeholder="أدخل اسمك الكامل" required>
                        <div class="invalid-feedback">
                            يرجى إدخال الاسم الكامل
                        </div>
                    </div>

                    
                    <div class="mb-3">
                        <label for="mobile_number" class="form-label">
                            رقم الجوال <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <img src="https://flagcdn.com/w20/sa.png" alt="السعودية" class="me-1">
                                +966
                            </span>
                            <input type="tel" class="form-control" id="mobile_number" name="mobile_number"
                                   placeholder="5xxxxxxxx" pattern="^5[0-9]{8}$" required>
                        </div>
                        <div class="form-text">
                            أدخل رقم الجوال بدون رمز الدولة (مثال: 501234567)
                        </div>
                        <div class="invalid-feedback">
                            يرجى إدخال رقم جوال صحيح يبدأ بـ 5 ويتكون من 9 أرقام
                        </div>
                    </div>

                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            البريد الإلكتروني <span class="text-muted">(اختياري)</span>
                        </label>
                        <input type="email" class="form-control" id="email" name="email"
                               placeholder="<EMAIL>">
                        <div class="invalid-feedback">
                            يرجى إدخال بريد إلكتروني صحيح
                        </div>
                    </div>

                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                  placeholder="أي تفاصيل إضافية تود إضافتها..."></textarea>
                        <div class="form-text">
                            يمكنك إضافة أي تفاصيل أو متطلبات خاصة للخدمة
                        </div>
                    </div>

                    
                    <div class="d-grid">
                        <button type="submit" class="btn brand-btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>
                            أرسل طلبك الآن
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // معالجة أزرار طلب الخدمة
    const requestButtons = document.querySelectorAll('.request-service-btn');
    const serviceIdInput = document.getElementById('service_id');
    const serviceNameDisplay = document.getElementById('service_name_display');
    const modalServiceName = document.getElementById('modal-service-name');

    requestButtons.forEach(button => {
        button.addEventListener('click', function() {
            const serviceId = this.dataset.serviceId;
            const serviceName = this.dataset.serviceName;

            if (serviceIdInput) serviceIdInput.value = serviceId;
            if (serviceNameDisplay) serviceNameDisplay.value = serviceName;
            if (modalServiceName) modalServiceName.textContent = serviceName;
        });
    });

    // معالجة إرسال نموذج طلب الخدمة
    const serviceRequestForm = document.getElementById('<?php echo e($formId); ?>');
    if (serviceRequestForm) {
        serviceRequestForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // التحقق من صحة النموذج
            if (!this.checkValidity()) {
                e.stopPropagation();
                this.classList.add('was-validated');
                return;
            }

            // التحقق من رقم الجوال السعودي
            const mobileInput = document.getElementById('mobile_number');
            const mobilePattern = /^5[0-9]{8}$/;

            if (mobileInput && !mobilePattern.test(mobileInput.value)) {
                mobileInput.setCustomValidity('يرجى إدخال رقم جوال صحيح يبدأ بـ 5 ويتكون من 9 أرقام');
                mobileInput.classList.add('is-invalid');
                return;
            } else if (mobileInput) {
                mobileInput.setCustomValidity('');
                mobileInput.classList.remove('is-invalid');
            }

            // إرسال النموذج عبر AJAX
            this.submitServiceRequest();
        });
    }

    // دالة إرسال طلب الخدمة عبر AJAX
    const serviceRequestForm = document.getElementById('<?php echo e($formId); ?>');
    if (serviceRequestForm) {
        serviceRequestForm.submitServiceRequest = function() {
            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;

            // تعطيل الزر وإظهار حالة التحميل
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';

            // إزالة رسائل الخطأ السابقة
            this.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
            this.querySelectorAll('.invalid-feedback').forEach(el => el.style.display = 'none');

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إظهار رسالة النجاح
                    this.showSuccessMessage(data.message, data.data);
                    // إعادة تعيين النموذج
                    this.reset();
                    // إغلاق المودال
                    const modal = bootstrap.Modal.getInstance(document.getElementById('<?php echo e($modalId); ?>'));
                    if (modal) modal.hide();
                } else {
                    // إظهار رسائل الخطأ
                    this.showValidationErrors(data.errors || {});
                    if (data.message) {
                        this.showErrorMessage(data.message);
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في إرسال طلب الخدمة:', error);
                this.showErrorMessage('حدث خطأ أثناء إرسال طلبك. يرجى المحاولة مرة أخرى.');
            })
            .finally(() => {
                // إعادة تفعيل الزر
                submitButton.disabled = false;
                submitButton.innerHTML = originalButtonText;
            });
        };

        // دالة إظهار رسالة النجاح
        serviceRequestForm.showSuccessMessage = function(message, data) {
            const alertHtml = `
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم بنجاح!</strong> ${message}
                    ${data && data.request_number ? `<br><small>رقم الطلب: ${data.request_number}</small>` : ''}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            // إضافة التنبيه في أعلى الصفحة
            const container = document.querySelector('.container') || document.body;
            container.insertAdjacentHTML('afterbegin', alertHtml);

            // التمرير إلى أعلى الصفحة
            window.scrollTo({ top: 0, behavior: 'smooth' });
        };

        // دالة إظهار رسالة خطأ عامة
        serviceRequestForm.showErrorMessage = function(message) {
            const alertHtml = `
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>خطأ!</strong> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            const modalBody = this.closest('.modal-body');
            if (modalBody) {
                modalBody.insertAdjacentHTML('afterbegin', alertHtml);
            }
        };

        // دالة إظهار أخطاء التحقق
        serviceRequestForm.showValidationErrors = function(errors) {
            Object.keys(errors).forEach(fieldName => {
                const field = this.querySelector(`[name="${fieldName}"]`);
                if (field) {
                    field.classList.add('is-invalid');
                    const feedback = field.parentNode.querySelector('.invalid-feedback');
                    if (feedback) {
                        feedback.textContent = errors[fieldName][0];
                        feedback.style.display = 'block';
                    }
                }
            });
        };
    }

    // إزالة رسائل الخطأ عند الكتابة
    const inputs = serviceRequestForm?.querySelectorAll('input, textarea');
    inputs?.forEach(input => {
        input.addEventListener('input', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('styles'); ?>
<style>
.modal-header.brand-bg-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%);
}

.form-control:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.btn.brand-btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn.brand-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--bs-primary-rgb), 0.3);
}

.input-group-text img {
    width: 20px;
    height: auto;
}

.alert-info {
    background-color: rgba(var(--bs-info-rgb), 0.1);
    border-color: rgba(var(--bs-info-rgb), 0.2);
}

.is-valid {
    border-color: var(--bs-success);
}

.is-invalid {
    border-color: var(--bs-danger);
}

@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-lg {
        max-width: none;
    }
}
</style>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Project\MotorLine_10\Modules/ServiceManagement\Resources/views/site/services/_request_form.blade.php ENDPATH**/ ?>
<?php

namespace Modules\OrderManagement\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Modules\UserManagement\Models\User;
use Modules\UserManagement\Models\Nationality;
use Modules\CarCatalog\Models\Car;

/**
 * نموذج الطلبات - يدير طلبات شراء السيارات (كاش أو تمويل)
 *
 * يخدم MOD-ORDER-MGMT ويتضمن:
 * - معلومات الطلب الأساسية
 * - تفاصيل الدفع والحجز
 * - معلومات العميل وقت الطلب
 * - تفاصيل التمويل (إذا كان الطلب تمويلي)
 * - ربط مع مستندات الطلب عبر spatie/laravel-medialibrary
 */
class Order extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, InteractsWithMedia;

    /**
     * الحقول القابلة للتعبئة الجماعية
     * بناءً على جدول orders في TS-FR.md (DB-TBL-012)
     */
    protected $fillable = [
        'user_id',
        'car_id',
        'order_number',
        'order_type',
        'status',
        'car_price_at_order',
        'reservation_amount',
        'remaining_amount',
        'payment_method',
        'payment_status',
        'payment_transaction_id',
        'customer_national_id',
        'customer_dob',
        'customer_nationality_id',
        'customer_address_details',
        'admin_notes',
        'finance_details',
        'assigned_employee_id',
        'payment_gateway_response',

        // حقول طلبات السيارات المخصصة
        'customer_name',
        'customer_phone',
        'customer_email',
        'requested_brand_id',
        'requested_model_id',
        'requested_year_id',
        'preferred_color',
        'additional_features',
        'budget_range',
        'delivery_preference',
        'notes',
        'order_date',
    ];

    /**
     * الحقول المخفية من التسلسل
     */
    protected $hidden = [
        'payment_gateway_response',
    ];

    /**
     * تحويل الحقول إلى أنواع بيانات محددة
     */
    protected $casts = [
        'customer_dob' => 'date',
        'finance_details' => 'array',
        'payment_gateway_response' => 'array',
        'car_price_at_order' => 'decimal:2',
        'reservation_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'order_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * العلاقة مع المستخدم (العميل)
     * علاقة متعدد لواحد - كل طلب ينتمي لعميل واحد
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * العلاقة مع السيارة المطلوبة
     * علاقة متعدد لواحد - كل طلب لسيارة واحدة
     */
    public function car()
    {
        return $this->belongsTo(Car::class);
    }

    /**
     * العلاقة مع الموظف المعين لمتابعة الطلب
     * علاقة متعدد لواحد اختيارية
     */
    public function assignedEmployee()
    {
        return $this->belongsTo(User::class, 'assigned_employee_id');
    }

    /**
     * العلاقة مع جنسية العميل وقت الطلب
     * علاقة متعدد لواحد اختيارية
     */
    public function customerNationality()
    {
        return $this->belongsTo(Nationality::class, 'customer_nationality_id');
    }

    /**
     * العلاقة مع الماركة المطلوبة (للطلبات المخصصة)
     * علاقة متعدد لواحد اختيارية
     */
    public function requestedBrand()
    {
        return $this->belongsTo(\Modules\CarCatalog\Models\Brand::class, 'requested_brand_id');
    }

    /**
     * العلاقة مع الموديل المطلوب (للطلبات المخصصة)
     * علاقة متعدد لواحد اختيارية
     */
    public function requestedModel()
    {
        return $this->belongsTo(\Modules\CarCatalog\Models\CarModel::class, 'requested_model_id');
    }

    /**
     * العلاقة مع سنة الصنع المطلوبة (للطلبات المخصصة)
     * علاقة متعدد لواحد اختيارية
     */
    public function requestedYear()
    {
        return $this->belongsTo(\Modules\CarCatalog\Models\ManufacturingYear::class, 'requested_year_id');
    }

    /**
     * نطاقات الاستعلام (Query Scopes)
     */

    /**
     * نطاق للطلبات المعلقة للمراجعة الإدارية
     */
    public function scopePendingReview($query)
    {
        return $query->where('status', 'pending_admin_review');
    }

    /**
     * نطاق للطلبات قيد المعالجة
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    /**
     * نطاق للطلبات المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * نطاق للطلبات النقدية
     */
    public function scopeCashOrders($query)
    {
        return $query->where('order_type', 'cash_reservation');
    }

    /**
     * نطاق لطلبات التمويل
     */
    public function scopeFinanceOrders($query)
    {
        return $query->where('order_type', 'finance_application');
    }

    /**
     * نطاق لطلبات السيارات المخصصة
     */
    public function scopeCustomCarRequests($query)
    {
        return $query->where('order_type', 'custom_car_request');
    }

    /**
     * دوال مساعدة (Helper Methods)
     */

    /**
     * توليد رقم طلب فريد
     */
    public static function generateOrderNumber()
    {
        $prefix = 'ORD';
        $year = date('Y');
        $month = date('m');

        // البحث عن آخر رقم طلب في الشهر الحالي
        $lastOrder = static::where('order_number', 'like', $prefix . $year . $month . '%')
            ->orderBy('order_number', 'desc')
            ->first();

        if ($lastOrder) {
            $lastNumber = intval(substr($lastOrder->order_number, -4));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . $month . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * التحقق من كون الطلب طلب تمويل
     */
    public function isFinanceOrder()
    {
        return $this->order_type === 'finance_application';
    }

    /**
     * التحقق من كون الطلب طلب نقدي
     */
    public function isCashOrder()
    {
        return $this->order_type === 'cash_reservation';
    }

    /**
     * التحقق من كون الطلب طلب سيارة مخصصة
     */
    public function isCustomCarRequest()
    {
        return $this->order_type === 'custom_car_request';
    }

    /**
     * الحصول على حالة الطلب بالعربية
     */
    public function getStatusInArabic()
    {
        $statuses = [
            'pending_payment' => 'في انتظار الدفع',
            'pending_admin_review' => 'في انتظار المراجعة الإدارية',
            'confirmed' => 'مؤكد',
            'processing' => 'قيد المعالجة',
            'awaiting_documents' => 'في انتظار المستندات',
            'approved' => 'موافق عليه',
            'rejected' => 'مرفوض',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            'payment_failed' => 'فشل الدفع',
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    /**
     * الحصول على حالة الدفع بالعربية
     */
    public function getPaymentStatusInArabic(): string
    {
        return match($this->payment_status) {
            'pending' => 'في الانتظار',
            'completed' => 'مكتمل',
            'failed' => 'فشل',
            'cancelled' => 'ملغي',
            'refunded' => 'مسترد',
            default => $this->payment_status
        };
    }

    /**
     * الحصول على طريقة الدفع بالعربية
     */
    public function getPaymentMethodInArabic(): string
    {
        return match($this->payment_method) {
            'online_payment' => 'دفع إلكتروني',
            'showroom_payment' => 'دفع في المعرض',
            'bank_transfer' => 'تحويل بنكي',
            default => $this->payment_method
        };
    }

    /**
     * التحقق من إمكانية إلغاء الطلب
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, [
            'pending_payment',
            'pending_admin_review'
        ]);
    }

    /**
     * التحقق من إمكانية تعديل الطلب
     */
    public function canBeModified(): bool
    {
        return in_array($this->status, [
            'pending_payment',
            'pending_admin_review'
        ]);
    }

    /**
     * الحصول على نسبة التقدم في الطلب
     */
    public function getProgressPercentage(): int
    {
        return match($this->status) {
            'pending_payment' => 25,
            'pending_admin_review' => 50,
            'confirmed' => 75,
            'completed' => 100,
            'cancelled', 'payment_failed' => 0,
            default => 0
        };
    }

    /**
     * الحصول على لون حالة الطلب للعرض
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            'pending_payment' => 'warning',
            'pending_admin_review' => 'info',
            'confirmed' => 'success',
            'completed' => 'primary',
            'cancelled', 'payment_failed' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * تنظيف الجلسة من بيانات الطلب
     */
    public static function clearOrderSession(): void
    {
        $sessionKeys = [
            'cash_order_car_id',
            'cash_order_step',
            'cash_order_personal_data',
            'cash_order_payment_data',
            'cash_order_documents_data',
            'cash_order_reservation_amount',
            'cash_order_temp_documents',
            'cash_order_confirmation_data'
        ];

        foreach ($sessionKeys as $key) {
            session()->forget($key);
        }
    }

    /**
     * تكوين مجموعات الوسائط لـ spatie/laravel-medialibrary
     */
    public function registerMediaCollections(): void
    {
        // مستندات الهوية الوطنية
        $this->addMediaCollection('national_id_front')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf'])
            ->singleFile();

        $this->addMediaCollection('national_id_back')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf'])
            ->singleFile();

        // رخصة القيادة
        $this->addMediaCollection('driving_license')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf'])
            ->singleFile();

        // شهادة الراتب (للتمويل)
        $this->addMediaCollection('salary_certificate')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf'])
            ->singleFile();

        // كشف حساب بنكي (للتمويل)
        $this->addMediaCollection('bank_statement')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf'])
            ->singleFile();

        // مستندات إضافية
        $this->addMediaCollection('additional_documents')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp', 'application/pdf']);
    }


}

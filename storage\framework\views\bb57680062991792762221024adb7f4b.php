<?php $__env->startSection('title', 'مبيعات الشركات - موتور لاين'); ?>

<?php $__env->startSection('meta_description', 'خدمات مبيعات الشركات في موتور لاين - احصل على أفضل العروض لشراء أساطيل السيارات للشركات والمؤسسات بأسعار تنافسية وخدمة متميزة'); ?>

<?php $__env->startSection('meta_keywords', 'مبيعات الشركات، أساطيل السيارات، شراء سيارات للشركات، عروض الشركات، موتور لاين'); ?>

<?php $__env->startSection('content'); ?>

<section class="hero-section bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-building me-3 text-warning"></i>
                        مبيعات الشركات
                    </h1>
                    <p class="lead mb-4">
                        سارع بطلب أسطول سياراتك من موتور لاين واحصل على أفضل العروض والأسعار التنافسية للشركات والمؤسسات
                    </p>
                    <div class="hero-features">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>أسعار تنافسية للكميات</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>خدمة ما بعد البيع</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>تسليم سريع</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="feature-item d-flex align-items-center">
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span>استشارة مجانية</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image text-center">
                    <img src="<?php echo e(asset('images/corporate-sales-hero.jpg')); ?>" 
                         alt="مبيعات الشركات" 
                         class="img-fluid rounded-3 shadow-lg"
                         style="max-height: 400px; object-fit: cover;">
                </div>
            </div>
        </div>
    </div>
</section>


<section class="services-section py-5">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title">خدماتنا للشركات</h2>
                <p class="section-subtitle text-muted">نقدم حلول شاملة لاحتياجات الشركات من السيارات</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="service-card h-100 text-center p-4 border rounded-3 shadow-sm">
                    <div class="service-icon mb-3">
                        <i class="fas fa-cars fa-3x text-primary"></i>
                    </div>
                    <h4 class="service-title">أساطيل متنوعة</h4>
                    <p class="service-description text-muted">
                        نوفر مجموعة واسعة من السيارات لتناسب احتياجات شركتك المختلفة
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="service-card h-100 text-center p-4 border rounded-3 shadow-sm">
                    <div class="service-icon mb-3">
                        <i class="fas fa-percentage fa-3x text-success"></i>
                    </div>
                    <h4 class="service-title">خصومات خاصة</h4>
                    <p class="service-description text-muted">
                        احصل على خصومات مميزة عند شراء كميات كبيرة من السيارات
                    </p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="service-card h-100 text-center p-4 border rounded-3 shadow-sm">
                    <div class="service-icon mb-3">
                        <i class="fas fa-headset fa-3x text-info"></i>
                    </div>
                    <h4 class="service-title">دعم مخصص</h4>
                    <p class="service-description text-muted">
                        فريق دعم مخصص لمتابعة طلبات الشركات وتقديم الاستشارة
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>


<section class="contact-form-section py-5 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-container bg-white rounded-3 shadow p-5">
                    <div class="text-center mb-4">
                        <h3 class="form-title">طلب عرض أسعار</h3>
                        <p class="form-subtitle text-muted">
                            يرجى تعبئة النموذج التالي لنقوم بالتواصل معك في أقرب وقت ممكن!
                        </p>
                    </div>

                    <form id="corporateEnquiryForm" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="row g-3">
                            
                            <div class="col-md-6">
                                <label for="contact_person_name" class="form-label">
                                    اسم مسؤول الاتصال <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="contact_person_name" 
                                       name="contact_person_name" 
                                       required
                                       placeholder="أدخل اسم مسؤول الاتصال">
                                <div class="invalid-feedback"></div>
                            </div>

                            
                            <div class="col-md-6">
                                <label for="company_name" class="form-label">
                                    اسم الشركة <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="company_name" 
                                       name="company_name" 
                                       required
                                       placeholder="أدخل اسم الشركة">
                                <div class="invalid-feedback"></div>
                            </div>

                            
                            <div class="col-md-6">
                                <label for="phone" class="form-label">
                                    رقم الجوال <span class="text-danger">*</span>
                                </label>
                                <input type="tel" 
                                       class="form-control" 
                                       id="phone" 
                                       name="phone" 
                                       required
                                       placeholder="05xxxxxxxx"
                                       pattern="^(05|5)[0-9]{8}$">
                                <div class="invalid-feedback"></div>
                            </div>

                            
                            <div class="col-md-6">
                                <label for="email" class="form-label">
                                    البريد الإلكتروني <span class="text-danger">*</span>
                                </label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       name="email" 
                                       required
                                       placeholder="<EMAIL>">
                                <div class="invalid-feedback"></div>
                            </div>

                            
                            <div class="col-md-6">
                                <label for="city" class="form-label">المدينة</label>
                                <select class="form-select" id="city" name="city">
                                    <option value="">اختر المدينة</option>
                                    <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($city); ?>"><?php echo e($city); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>

                            
                            <div class="col-12">
                                <label for="request_details" class="form-label">
                                    تفاصيل الطلب <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control" 
                                          id="request_details" 
                                          name="request_details" 
                                          rows="4" 
                                          required
                                          placeholder="مثال: 20 سيارة تويوتا كامري، لون أبيض، موديل 2024"></textarea>
                                <div class="invalid-feedback"></div>
                                <small class="form-text text-muted">
                                    يرجى ذكر نوع السيارات المطلوبة، الكمية، اللون المفضل، وأي متطلبات أخرى
                                </small>
                            </div>

                            
                            <div class="col-12 text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg px-5" id="submitBtn">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    أرسل طلبك الآن
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>


<div class="modal fade" id="successModal" tabindex="-1" aria-labelledby="successModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 text-center">
                <div class="w-100">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5 class="modal-title" id="successModalLabel">تم إرسال طلبك بنجاح!</h5>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body text-center">
                <p class="mb-0">سنقوم بالتواصل معك في أقرب وقت ممكن!</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">حسناً</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 60vh;
}

.min-vh-50 {
    min-height: 50vh;
}

.feature-item {
    margin-bottom: 0.5rem;
}

.service-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
}

.form-container {
    border-top: 4px solid var(--bs-primary);
}

.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 1rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: var(--bs-primary);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('corporateEnquiryForm');
    const submitBtn = document.getElementById('submitBtn');
    const successModal = new bootstrap.Modal(document.getElementById('successModal'));

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // تعطيل زر الإرسال
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
        
        // إزالة رسائل الخطأ السابقة
        clearErrors();
        
        // إرسال البيانات
        const formData = new FormData(form);
        
        fetch('<?php echo e(route("site.corporate.submit")); ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إظهار رسالة النجاح
                successModal.show();
                // إعادة تعيين النموذج
                form.reset();
            } else {
                // إظهار رسائل الخطأ
                if (data.errors) {
                    showErrors(data.errors);
                } else {
                    alert(data.message || 'حدث خطأ أثناء إرسال الطلب');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
        })
        .finally(() => {
            // إعادة تفعيل زر الإرسال
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>أرسل طلبك الآن';
        });
    });
    
    function clearErrors() {
        const errorElements = form.querySelectorAll('.is-invalid');
        errorElements.forEach(element => {
            element.classList.remove('is-invalid');
        });
        
        const feedbackElements = form.querySelectorAll('.invalid-feedback');
        feedbackElements.forEach(element => {
            element.textContent = '';
        });
    }
    
    function showErrors(errors) {
        for (const [field, messages] of Object.entries(errors)) {
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.classList.add('is-invalid');
                const feedback = input.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.textContent = messages[0];
                }
            }
        }
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\Modules/CorporateSales\Resources/views/site/index.blade.php ENDPATH**/ ?>
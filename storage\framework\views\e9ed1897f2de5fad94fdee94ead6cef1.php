<?php $__env->startSection('title', 'قائمة المفضلة - موتور لاين'); ?>

<?php $__env->startSection('meta_description', 'عرض قائمة السيارات المفضلة التي قمت بحفظها في موتور لاين'); ?>

<?php $__env->startSection('content'); ?>
<div class="container py-4">
    
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="<?php echo e(route('site.home')); ?>" class="brand-link">الرئيسية</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">قائمة المفضلة</li>
        </ol>
    </nav>

    
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title">
                    <i class="fas fa-heart text-danger me-2"></i>
                    قائمة المفضلة
                </h1>
                <p class="page-subtitle text-muted">
                    السيارات التي قمت بحفظها لمراجعتها لاحقاً
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="favorites-stats">
                    <span class="badge bg-primary fs-6">
                        <?php echo e($favoriteCars->total()); ?> سيارة مفضلة
                    </span>
                </div>
            </div>
        </div>
    </div>

    <?php if($favoriteCars->count() > 0): ?>
        
        <div class="row">
            <?php $__currentLoopData = $favoriteCars; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $car): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="car-card h-100">
                        
                        <div class="car-image-container">
                            <?php if($car->getFirstMediaUrl('car_images')): ?>
                                <img src="<?php echo e($car->getFirstMediaUrl('car_images', 'medium')); ?>" 
                                     alt="<?php echo e($car->display_name); ?>" 
                                     class="car-image">
                            <?php else: ?>
                                <div class="car-image-placeholder">
                                    <i class="fas fa-car fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>

                            
                            <button class="favorite-btn active" 
                                    onclick="removeFavorite(<?php echo e($car->id); ?>)"
                                    title="إزالة من المفضلة">
                                <i class="fas fa-heart"></i>
                            </button>

                            
                            <?php if($car->is_sold): ?>
                                <span class="car-status-badge sold">مباعة</span>
                            <?php elseif($car->is_featured): ?>
                                <span class="car-status-badge featured">مميزة</span>
                            <?php endif; ?>
                        </div>

                        
                        <div class="car-details">
                            <h5 class="car-title">
                                <a href="<?php echo e(route('site.cars.show', $car->id)); ?>" class="brand-link">
                                    <?php echo e($car->display_name); ?>

                                </a>
                            </h5>

                            <div class="car-specs">
                                <div class="spec-item">
                                    <i class="fas fa-calendar-alt text-muted"></i>
                                    <span><?php echo e($car->manufacturingYear->year ?? 'غير محدد'); ?></span>
                                </div>
                                <div class="spec-item">
                                    <i class="fas fa-gas-pump text-muted"></i>
                                    <span><?php echo e($car->fuelType->name ?? 'غير محدد'); ?></span>
                                </div>
                                <div class="spec-item">
                                    <i class="fas fa-car text-muted"></i>
                                    <span><?php echo e($car->bodyType->name ?? 'غير محدد'); ?></span>
                                </div>
                            </div>

                            <div class="car-price">
                                <?php if($car->price): ?>
                                    <span class="price-amount"><?php echo e(number_format($car->price)); ?> ريال</span>
                                <?php else: ?>
                                    <span class="price-contact">اتصل للاستفسار عن السعر</span>
                                <?php endif; ?>
                            </div>

                            
                            <div class="car-actions mt-3">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <a href="<?php echo e(route('site.cars.show', $car->id)); ?>" 
                                           class="btn btn-primary w-100 btn-sm">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض التفاصيل
                                        </a>
                                    </div>
                                    <div class="col-3">
                                        <button class="btn btn-outline-secondary w-100 btn-sm" 
                                                onclick="addToCompare(<?php echo e($car->id); ?>)"
                                                title="إضافة للمقارنة">
                                            <i class="fas fa-balance-scale"></i>
                                        </button>
                                    </div>
                                    <div class="col-3">
                                        <button class="btn btn-outline-danger w-100 btn-sm" 
                                                onclick="removeFavorite(<?php echo e($car->id); ?>)"
                                                title="إزالة من المفضلة">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            
                            <div class="favorite-date mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    أضيفت في <?php echo e($car->pivot->created_at->format('d/m/Y')); ?>

                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        
        <?php if($favoriteCars->hasPages()): ?>
            <div class="d-flex justify-content-center mt-4">
                <?php echo e($favoriteCars->links()); ?>

            </div>
        <?php endif; ?>

        
        <div class="bulk-actions mt-4 text-center">
            <button class="btn btn-outline-danger" onclick="clearAllFavorites()">
                <i class="fas fa-trash me-2"></i>
                مسح جميع المفضلة
            </button>
            <a href="<?php echo e(route('site.cars.index')); ?>" class="btn btn-primary ms-2">
                <i class="fas fa-plus me-2"></i>
                إضافة المزيد من السيارات
            </a>
        </div>

    <?php else: ?>
        
        <div class="empty-state text-center py-5">
            <div class="empty-icon mb-4">
                <i class="fas fa-heart fa-5x text-muted"></i>
            </div>
            <h3 class="empty-title">لا توجد سيارات في المفضلة</h3>
            <p class="empty-description text-muted mb-4">
                لم تقم بإضافة أي سيارات إلى قائمة المفضلة بعد.<br>
                تصفح مجموعتنا الواسعة من السيارات وأضف ما يعجبك إلى المفضلة.
            </p>
            <a href="<?php echo e(route('site.cars.index')); ?>" class="btn btn-primary btn-lg">
                <i class="fas fa-car me-2"></i>
                تصفح السيارات
            </a>
        </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* أنماط صفحة المفضلة */
.page-header {
    border-bottom: 2px solid var(--light-bg);
    padding-bottom: 1rem;
}

.page-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    font-size: 1.1rem;
}

.favorites-stats .badge {
    padding: 0.5rem 1rem;
}

.car-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.car-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.car-image-container {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.car-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.car-card:hover .car-image {
    transform: scale(1.05);
}

.car-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-bg);
}

.favorite-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255,255,255,0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #dc3545;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.favorite-btn:hover {
    background: white;
    transform: scale(1.1);
}

.car-status-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
}

.car-status-badge.sold {
    background: #dc3545;
}

.car-status-badge.featured {
    background: #ffc107;
    color: #000;
}

.car-details {
    padding: 1.5rem;
}

.car-title {
    font-weight: 600;
    margin-bottom: 1rem;
}

.car-title a {
    color: var(--text-color);
    text-decoration: none;
}

.car-title a:hover {
    color: var(--primary-color);
}

.car-specs {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.spec-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.car-price {
    margin-bottom: 1rem;
}

.price-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.price-contact {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-style: italic;
}

.favorite-date {
    border-top: 1px solid var(--light-bg);
    padding-top: 0.75rem;
}

.empty-state {
    max-width: 500px;
    margin: 0 auto;
}

.empty-icon {
    opacity: 0.3;
}

.empty-title {
    color: var(--text-color);
    margin-bottom: 1rem;
}

.bulk-actions {
    border-top: 2px solid var(--light-bg);
    padding-top: 2rem;
}

@media (max-width: 768px) {
    .car-specs {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .favorites-stats {
        margin-top: 1rem;
    }
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// إزالة سيارة من المفضلة
function removeFavorite(carId) {
    if (!confirm('هل أنت متأكد من إزالة هذه السيارة من المفضلة؟')) {
        return;
    }

    fetch(`/favorites/remove/${carId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إعادة تحميل الصفحة لإزالة السيارة من القائمة
            location.reload();
        } else {
            alert(data.message || 'حدث خطأ أثناء إزالة السيارة من المفضلة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إزالة السيارة من المفضلة');
    });
}

// إضافة سيارة للمقارنة
function addToCompare(carId) {
    fetch(`/compare/add/${carId}`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إضافة السيارة للمقارنة بنجاح');
        } else {
            alert(data.message || 'حدث خطأ أثناء إضافة السيارة للمقارنة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إضافة السيارة للمقارنة');
    });
}

// مسح جميع المفضلة
function clearAllFavorites() {
    if (!confirm('هل أنت متأكد من مسح جميع السيارات من المفضلة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    // يمكن تنفيذ هذا لاحقاً
    alert('سيتم تنفيذ هذه الميزة قريباً');
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('site.layouts.site_layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Project\MotorLine_10\Modules/CarCatalog\Resources/views/site/favorites/index.blade.php ENDPATH**/ ?>